-- Enhanced SQL Query with Field Comments
-- Note: Replace the placeholder comments with actual field comments from your database schema

SELECT DISTINCT TOP 100 PERCENT
    CI.CardName,        -- [Add actual comment for CardName field]
    CI.CardNo,          -- [Add actual comment for CardNo field]
    R.FLOOR AS FLOOR,   -- [Add actual comment for FLOOR field]
    CC.CompanyId,       -- [Add actual comment for CompanyId field]
    CC.[Name] AS CompanieName,  -- [Add actual comment for Company Name field]
    CI.DEPOSITCHECKIN,  -- [Add actual comment for DEPOSITCHECKIN field]
    CI.CheckinId AS Id, -- [Add actual comment for CheckinId field]
    CI.CheckinNo,       -- [Add actual comment for CheckinNo field]
    CI.CheckinDate,     -- [Add actual comment for CheckinDate field]
    '' AS CheckinDate2, -- Formatted CheckinDate
    CI.BedBookNo,       -- [Add actual comment for BedBookNo field]
    CI.BaseId,          -- [Add actual comment for BaseId field]
    B1.Name AS BaseName,    -- [Add actual comment for Base Name field]
    CI.BuildingId,      -- [Add actual comment for BuildingId field]
    B2.Name AS BuildingName,    -- [Add actual comment for Building Name field]
    CI.RoomTypeId,      -- [Add actual comment for RoomTypeId field]
    RT.Name AS RoomTypeName,    -- [Add actual comment for RoomType Name field]
    CI.RoomId,          -- [Add actual comment for RoomId field]
    R.Name AS RoomName, -- [Add actual comment for Room Name field]
    CI.BedTypeId,       -- [Add actual comment for BedTypeId field]
    BT.Name AS BedTypeName,     -- [Add actual comment for BedType Name field]
    CI.BedId,           -- [Add actual comment for BedId field]
    B3.BedNo,           -- [Add actual comment for BedNo field]
    CI.Name,            -- [Add actual comment for Name field]
    CI.IdCardNo,        -- [Add actual comment for IdCardNo field]
    CI.Mobile,          -- [Add actual comment for Mobile field]
    CI.Gender,          -- [Add actual comment for Gender field]
    DG.Name AS GenderName,      -- [Add actual comment for Gender Name field]
    CI.BeginDate,       -- [Add actual comment for BeginDate field]
    '' AS BeginDate2,   -- Formatted BeginDate
    CI.EndDate,         -- [Add actual comment for EndDate field]
    '' AS EndDate2,     -- Formatted EndDate
    CI.IsEnabled,       -- [Add actual comment for IsEnabled field]
    CI.Description,     -- [Add actual comment for Description field]
    CI.Creator,         -- [Add actual comment for Creator field]
    CU.Name AS CreatorName,     -- [Add actual comment for Creator Name field]
    CI.CreatedTime,     -- [Add actual comment for CreatedTime field]
    '' AS CreatedTime2, -- Formatted CreatedTime
    CI.Modifier,        -- [Add actual comment for Modifier field]
    MU.Name AS ModifierName,    -- [Add actual comment for Modifier Name field]
    CI.ModifiedTime,    -- [Add actual comment for ModifiedTime field]
    '' AS ModifiedTime2,    -- Formatted ModifiedTime
    CI.IsRetain         -- [Add actual comment for IsRetain field]
FROM CSO_Checkins CI    -- [Add actual comment for CSO_Checkins table]
LEFT JOIN CSB_Bases B1 ON CI.BaseId = B1.BaseId    -- [Add actual comment for CSB_Bases table]
LEFT JOIN CSB_Buildings B2 ON CI.BuildingId = B2.BuildingId    -- [Add actual comment for CSB_Buildings table]
LEFT JOIN CSB_RoomTypes RT ON CI.RoomTypeId = RT.RoomTypeId    -- [Add actual comment for CSB_RoomTypes table]
LEFT JOIN CSB_Rooms R ON CI.RoomId = R.RoomId    -- [Add actual comment for CSB_Rooms table]
LEFT JOIN CSB_BedTypes BT ON CI.BedTypeId = BT.BedTypeId    -- [Add actual comment for CSB_BedTypes table]
LEFT JOIN CSB_Beds B3 ON CI.BedId = B3.BedId    -- [Add actual comment for CSB_Beds table]
LEFT JOIN VD_Dictionaries DG ON CI.Gender = DG.DictionaryKey 
    AND UPPER(DG.DictionaryType) = 'GENDER' 
    AND UPPER(DG.LanguageCode) = 'ZH-CN'    -- [Add actual comment for VD_Dictionaries table]
LEFT JOIN O_Users CU ON CI.Creator = CU.UserId    -- [Add actual comment for O_Users table - Creator]
LEFT JOIN O_Users MU ON CI.Modifier = MU.UserId    -- [Add actual comment for O_Users table - Modifier]
LEFT JOIN CSC_Companies CC ON CI.CompanyId = CC.CompanyId    -- [Add actual comment for CSC_Companies table]
WHERE
    B1.[IsEnabled]=1    -- Base is enabled
    AND B2.[IsEnabled]=1    -- Building is enabled
    AND B3.[IsEnabled]=1    -- Bed is enabled
    AND R.[IsEnabled]=1     -- Room is enabled
    AND B1.BaseId IN (
        SELECT TOP 100 PERCENT BaseId 
        FROM CSB_Receptionists 
        WHERE UserId = '91508065-9E2A-4B0D-A4DB-70FC39A52E80'    -- Filter by receptionist's bases
    )
AND (
    -- Date range filtering logic
    (CI.EndDate >= '2025-07-23' AND CI.BeginDate >= '2025-07-23' AND CI.BeginDate <= '2025-07-23')
    OR (CI.EndDate >= '2025-07-23' AND CI.BeginDate <= '2025-07-23')
    OR (CI.BeginDate <= '2025-07-23' AND CI.EndDate >= '2025-07-23')
    OR (CI.BeginDate >= '2025-07-23' AND CI.EndDate <= '2025-07-23')
)
-- 隐藏的数据 (Hidden data)
-- and CI.IsRetain = 'true'    -- Uncomment to filter retained records only
;
