-- 会员卡类型统计查询 - 中文显示
SELECT 
    CASE 
        WHEN type_classify = 'MEMBER_CARD' THEN '会员卡'
        WHEN type_classify = 'MANAGE_CARD' THEN '管理卡'
        WHEN type_classify = 'WELFARE_CARD' THEN '福利卡'
        WHEN type_classify = 'GIVE_CARD' THEN '赠送卡'
        WHEN type_classify = 'OWNER_CARD' THEN '业主卡'
        WHEN type_classify = 'VIP_CARD' THEN '贵宾卡'
        ELSE type_classify  -- 如果有其他未定义的类型，显示原值
    END AS 卡片类型,
    COUNT(1) AS 数量
FROM main_membership_card_type 
GROUP BY type_classify
ORDER BY COUNT(1) DESC;  -- 按数量降序排列，可选
