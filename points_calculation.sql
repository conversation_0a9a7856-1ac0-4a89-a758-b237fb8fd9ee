-- 积分计算查询
-- 计算从2022-09-23 14:55:11到2023-02-14 15:39:22的积分

DECLARE @StartDate DATETIME = '2022-09-23 14:55:11';
DECLARE @EndDate DATETIME = '2023-02-14 15:39:22';
DECLARE @TotalDays INT;
DECLARE @TotalPoints DECIMAL(10,5) = 0;

-- 计算总天数
SET @TotalDays = DATEDIFF(DAY, @StartDate, @EndDate);

SELECT 
    @StartDate AS 开始时间,
    @EndDate AS 结束时间,
    @TotalDays AS 总天数;

-- 分段计算积分
WITH PointsCalculation AS (
    SELECT 
        CASE 
            WHEN @TotalDays <= 900 THEN 
                -- 全部在普通卡范围内
                @TotalDays * 0.00028
            WHEN @TotalDays <= 2200 THEN 
                -- 普通卡900天 + 银卡剩余天数
                900 * 0.00028 + (@TotalDays - 900) * 0.00033
            WHEN @TotalDays <= 3300 THEN 
                -- 普通卡900天 + 银卡1300天 + 金卡剩余天数
                900 * 0.00028 + 1300 * 0.00033 + (@TotalDays - 2200) * 0.00038
            ELSE 
                -- 普通卡900天 + 银卡1300天 + 金卡1100天 + 钻石卡剩余天数
                900 * 0.00028 + 1300 * 0.00033 + 1100 * 0.00038 + (@TotalDays - 3300) * 0.00043
        END AS 总积分
)
SELECT 
    @TotalDays AS 实际天数,
    CASE 
        WHEN @TotalDays <= 900 THEN '普通卡'
        WHEN @TotalDays <= 2200 THEN '银卡'
        WHEN @TotalDays <= 3300 THEN '金卡'
        ELSE '钻石卡'
    END AS 当前卡级别,
    总积分,
    ROUND(总积分, 5) AS 积分_保留5位小数
FROM PointsCalculation;

-- 详细分段计算展示
SELECT 
    '计算详情' AS 说明,
    @TotalDays AS 总天数,
    CASE 
        WHEN @TotalDays <= 900 THEN 
            CONCAT(@TotalDays, '天 × 0.00028 = ', @TotalDays * 0.00028)
        WHEN @TotalDays <= 2200 THEN 
            CONCAT('900天 × 0.00028 + ', (@TotalDays - 900), '天 × 0.00033 = ', 
                   900 * 0.00028 + (@TotalDays - 900) * 0.00033)
        WHEN @TotalDays <= 3300 THEN 
            CONCAT('900天 × 0.00028 + 1300天 × 0.00033 + ', (@TotalDays - 2200), '天 × 0.00038 = ', 
                   900 * 0.00028 + 1300 * 0.00033 + (@TotalDays - 2200) * 0.00038)
        ELSE 
            CONCAT('900天 × 0.00028 + 1300天 × 0.00033 + 1100天 × 0.00038 + ', (@TotalDays - 3300), '天 × 0.00043 = ', 
                   900 * 0.00028 + 1300 * 0.00033 + 1100 * 0.00038 + (@TotalDays - 3300) * 0.00043)
    END AS 计算公式;
