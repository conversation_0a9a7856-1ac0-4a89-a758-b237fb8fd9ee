-- 分析 CSO_Checkins 记录数量差异的验证查询

-- 1. 原始表总记录数
SELECT COUNT(*) AS '原始入住表总记录数'
FROM dbo.CSO_Checkins;

-- 2. 权限范围内的记录数
SELECT COUNT(*) AS '权限范围内记录数'
FROM CSO_Checkins CI
WHERE CI.BaseId IN (
    SELECT BaseId FROM CSB_Receptionists 
    WHERE UserId = '91508065-9E2A-4B0D-A4DB-70FC39A52E80'
);

-- 3. 加上启用状态过滤后的记录数
SELECT COUNT(*) AS '启用状态过滤后记录数'
FROM CSO_Checkins CI
LEFT JOIN CSB_Bases B1 ON CI.BaseId = B1.BaseId
LEFT JOIN CSB_Buildings B2 ON CI.BuildingId = B2.BuildingId
LEFT JOIN CSB_Rooms R ON CI.RoomId = R.RoomId
LEFT JOIN CSB_Beds B3 ON CI.BedId = B3.BedId
WHERE B1.[IsEnabled]=1
    AND B2.[IsEnabled]=1
    AND B3.[IsEnabled]=1
    AND R.[IsEnabled]=1
    AND B1.BaseId IN (
        SELECT BaseId FROM CSB_Receptionists 
        WHERE UserId = '91508065-9E2A-4B0D-A4DB-70FC39A52E80'
    );

-- 4. 检查数据完整性问题
-- 4.1 检查基地关联问题
SELECT COUNT(*) AS '基地关联缺失的记录数'
FROM CSO_Checkins CI
LEFT JOIN CSB_Bases B1 ON CI.BaseId = B1.BaseId
WHERE B1.BaseId IS NULL;

-- 4.2 检查建筑物关联问题
SELECT COUNT(*) AS '建筑物关联缺失的记录数'
FROM CSO_Checkins CI
LEFT JOIN CSB_Buildings B2 ON CI.BuildingId = B2.BuildingId
WHERE B2.BuildingId IS NULL;

-- 4.3 检查房间关联问题
SELECT COUNT(*) AS '房间关联缺失的记录数'
FROM CSO_Checkins CI
LEFT JOIN CSB_Rooms R ON CI.RoomId = R.RoomId
WHERE R.RoomId IS NULL;

-- 4.4 检查床位关联问题
SELECT COUNT(*) AS '床位关联缺失的记录数'
FROM CSO_Checkins CI
LEFT JOIN CSB_Beds B3 ON CI.BedId = B3.BedId
WHERE B3.BedId IS NULL;

-- 5. 检查禁用状态的影响
-- 5.1 禁用基地的影响
SELECT COUNT(*) AS '关联到禁用基地的记录数'
FROM CSO_Checkins CI
INNER JOIN CSB_Bases B1 ON CI.BaseId = B1.BaseId
WHERE B1.[IsEnabled] = 0;

-- 5.2 禁用建筑物的影响
SELECT COUNT(*) AS '关联到禁用建筑物的记录数'
FROM CSO_Checkins CI
INNER JOIN CSB_Buildings B2 ON CI.BuildingId = B2.BuildingId
WHERE B2.[IsEnabled] = 0;

-- 5.3 禁用房间的影响
SELECT COUNT(*) AS '关联到禁用房间的记录数'
FROM CSO_Checkins CI
INNER JOIN CSB_Rooms R ON CI.RoomId = R.RoomId
WHERE R.[IsEnabled] = 0;

-- 5.4 禁用床位的影响
SELECT CI.*
FROM CSO_Checkins CI
INNER JOIN CSB_Beds B3 ON CI.BedId = B3.BedId
WHERE B3.[IsEnabled] = 0;
