-- 入住信息综合查询 - 带详细中文注释
-- 业务目的：查询指定前台接待员权限范围内，特定日期的所有入住记录及相关信息

SELECT DISTINCT TOP 100 PERCENT
    CI.CardName,                    -- 持卡人姓名
    CI.CardNo,                      -- 卡片编号
    R.FLOOR AS FLOOR,               -- 房间所在楼层
    CC.CompanyId,                   -- 所属公司ID
    CC.[Name] AS CompanieName,      -- 所属公司名称
    CI.DEPOSITCHECKIN,              -- 押金入住标识
    CI.CheckinId AS Id,             -- 入住记录唯一标识
    CI.CheckinNo,                   -- 入住单号
    CI.CheckinDate,                 -- 入住登记日期
    '' AS CheckinDate2,             -- 入住日期格式化字段（预留）
    CI.BedBookNo,                   -- 床位预订编号
    CI.BaseId,                      -- 所属基地ID
    B1.Name AS BaseName,            -- 所属基地名称
    CI.BuildingId,                  -- 所属建筑物ID
    B2.Name AS BuildingName,        -- 所属建筑物名称
    CI.RoomTypeId,                  -- 房间类型ID
    RT.Name AS RoomTypeName,        -- 房间类型名称
    CI.RoomId,                      -- 房间ID
    R.Name AS RoomName,             -- 房间名称/房间号
    CI.BedTypeId,                   -- 床位类型ID
    BT.Name AS BedTypeName,         -- 床位类型名称
    CI.BedId,                       -- 床位ID
    B3.BedNo,                       -- 床位编号
    CI.Name,                        -- 入住人姓名
    CI.IdCardNo,                    -- 入住人身份证号
    CI.Mobile,                      -- 入住人手机号码
    CI.Gender,                      -- 入住人性别代码
    DG.Name AS GenderName,          -- 入住人性别名称
    CI.BeginDate,                   -- 入住开始日期
    '' AS BeginDate2,               -- 入住开始日期格式化字段（预留）
    CI.EndDate,                     -- 入住结束日期
    '' AS EndDate2,                 -- 入住结束日期格式化字段（预留）
    CI.IsEnabled,                   -- 记录是否有效
    CI.Description,                 -- 入住备注说明
    CI.Creator,                     -- 记录创建人ID
    CU.Name AS CreatorName,         -- 记录创建人姓名
    CI.CreatedTime,                 -- 记录创建时间
    '' AS CreatedTime2,             -- 创建时间格式化字段（预留）
    CI.Modifier,                    -- 记录最后修改人ID
    MU.Name AS ModifierName,        -- 记录最后修改人姓名
    CI.ModifiedTime,                -- 记录最后修改时间
    '' AS ModifiedTime2,            -- 修改时间格式化字段（预留）
    CI.IsRetain                     -- 是否保留记录标识
FROM CSO_Checkins CI                -- 主表：入住信息表
LEFT JOIN CSB_Bases B1 ON CI.BaseId = B1.BaseId                    -- 关联基地信息表
LEFT JOIN CSB_Buildings B2 ON CI.BuildingId = B2.BuildingId        -- 关联建筑物信息表
LEFT JOIN CSB_RoomTypes RT ON CI.RoomTypeId = RT.RoomTypeId        -- 关联房间类型表
LEFT JOIN CSB_Rooms R ON CI.RoomId = R.RoomId                      -- 关联房间信息表
LEFT JOIN CSB_BedTypes BT ON CI.BedTypeId = BT.BedTypeId           -- 关联床位类型表
LEFT JOIN CSB_Beds B3 ON CI.BedId = B3.BedId                       -- 关联床位信息表
LEFT JOIN VD_Dictionaries DG ON CI.Gender = DG.DictionaryKey       -- 关联字典表（获取性别中文名称）
    AND UPPER(DG.DictionaryType) = 'GENDER' 
    AND UPPER(DG.LanguageCode) = 'ZH-CN'
LEFT JOIN O_Users CU ON CI.Creator = CU.UserId                     -- 关联用户表（获取创建人信息）
LEFT JOIN O_Users MU ON CI.Modifier = MU.UserId                    -- 关联用户表（获取修改人信息）
LEFT JOIN CSC_Companies CC ON CI.CompanyId = CC.CompanyId          -- 关联公司信息表
WHERE
    B1.[IsEnabled]=1                -- 基地必须是启用状态
    AND B2.[IsEnabled]=1            -- 建筑物必须是启用状态
    AND B3.[IsEnabled]=1            -- 床位必须是启用状态
    AND R.[IsEnabled]=1             -- 房间必须是启用状态
    AND B1.BaseId IN (              -- 权限控制：只查询当前用户有权限管理的基地
        SELECT TOP 100 PERCENT BaseId 
        FROM CSB_Receptionists 
        WHERE UserId = '91508065-9E2A-4B0D-A4DB-70FC39A52E80'
    )
AND (
    -- 日期范围过滤：查询与2025-07-23这一天有交集的所有入住记录
    -- 包括以下情况：
    -- 1. 当天开始且当天结束的入住
    (CI.EndDate >= '2025-07-23' AND CI.BeginDate >= '2025-07-23' AND CI.BeginDate <= '2025-07-23')
    -- 2. 在此日期之前开始，在此日期或之后结束的入住
    OR (CI.EndDate >= '2025-07-23' AND CI.BeginDate <= '2025-07-23')
    -- 3. 跨越此日期的入住（开始日期在此日期之前或当天，结束日期在此日期或之后）
    OR (CI.BeginDate <= '2025-07-23' AND CI.EndDate >= '2025-07-23')
    -- 4. 在此日期范围内的入住
    OR (CI.BeginDate >= '2025-07-23' AND CI.EndDate <= '2025-07-23')
)
-- 可选过滤条件（当前被注释）
-- and CI.IsRetain = 'true'        -- 取消注释可仅显示标记为保留的记录
;
